
import React, { useEffect, useRef } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>ie, Settings, Shield, Eye, BarChart3, Mail, Phone } from 'lucide-react';

const Cookies = () => {
  const parallaxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (parallaxRef.current) {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.3;
        parallaxRef.current.style.transform = `translateY(${rate}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const cookieTypes = [
    {
      icon: Shield,
      title: 'Essential Cookies',
      description: 'These cookies are necessary for the website to function and cannot be switched off.',
      examples: ['User authentication', 'Security tokens', 'Shopping cart contents'],
      retention: 'Session or up to 1 year'
    },
    {
      icon: BarChart3,
      title: 'Analytics Cookies',
      description: 'These cookies help us understand how visitors use our website.',
      examples: ['Google Analytics', 'Page views', 'User behavior tracking'],
      retention: 'Up to 2 years'
    },
    {
      icon: Settings,
      title: 'Functional Cookies',
      description: 'These cookies enable enhanced functionality and personalization.',
      examples: ['Language preferences', 'Chat widgets', 'Social media features'],
      retention: 'Up to 1 year'
    },
    {
      icon: Eye,
      title: 'Marketing Cookies',
      description: 'These cookies are used to deliver personalized advertisements.',
      examples: ['Facebook Pixel', 'Google Ads', 'Retargeting pixels'],
      retention: 'Up to 2 years'
    }
  ];

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-20">
        {/* Hero Section with Parallax */}
        <div className="relative h-64 overflow-hidden">
          <div 
            ref={parallaxRef}
            className="absolute inset-0 w-full h-80"
            style={{
              backgroundImage: 'url(https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png)',
              backgroundSize: 'cover',
              backgroundPosition: 'center'
            }}
          />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/80 to-pink-600/80" />
          <div className="relative z-10 flex items-center justify-center h-full text-white px-4">
            <div className="text-center max-w-3xl mx-auto">
              <Badge className="mb-4 bg-white/20 text-white px-4 py-2">
                <Cookie className="w-4 h-4 mr-2" />
                Cookie Policy
              </Badge>
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Cookie Policy</h1>
              <p className="text-lg md:text-xl opacity-90">
                Learn about how we use cookies to improve your experience
              </p>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <Card className="mb-12 shadow-lg border-0">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold mb-4">Cookie Policy</h2>
                  <p className="text-gray-600 text-lg">Last Updated: January 1, 2024</p>
                </div>
                <div className="prose prose-lg max-w-none text-gray-700">
                  <p>
                    This Cookie Policy explains how SafariSole Tours uses cookies and similar technologies when you visit our website. We use cookies to enhance your browsing experience, analyze website traffic, and provide personalized content.
                  </p>
                  <p>
                    By continuing to use our website, you consent to our use of cookies as described in this policy. You can control and manage cookies through your browser settings.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* What Are Cookies */}
            <Card className="mb-12 shadow-lg border-0">
              <CardHeader>
                <CardTitle className="text-2xl">What Are Cookies?</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-gray-700 mb-4">
                  Cookies are small text files that are stored on your device when you visit a website. They help websites remember information about your visit, such as your preferred language and other settings. This makes your next visit easier and the site more useful to you.
                </p>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Types of Cookies:</h4>
                  <ul className="list-disc list-inside space-y-1 text-gray-700">
                    <li><strong>Session Cookies:</strong> Temporary cookies that expire when you close your browser</li>
                    <li><strong>Persistent Cookies:</strong> Cookies that remain on your device until they expire or are deleted</li>
                    <li><strong>First-party Cookies:</strong> Set by our website directly</li>
                    <li><strong>Third-party Cookies:</strong> Set by external services we use (analytics, advertising)</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Cookie Types */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
              {cookieTypes.map((type, index) => (
                <Card key={index} className="shadow-lg border-0 hover:shadow-xl transition-shadow">
                  <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50">
                    <CardTitle className="flex items-center text-xl">
                      <div className="bg-purple-100 p-2 rounded-lg mr-3">
                        <type.icon className="h-6 w-6 text-purple-600" />
                      </div>
                      {type.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <p className="text-gray-700 mb-4">{type.description}</p>
                    <div className="space-y-2">
                      <h4 className="font-semibold">Examples:</h4>
                      <ul className="list-disc list-inside text-sm text-gray-600">
                        {type.examples.map((example, i) => (
                          <li key={i}>{example}</li>
                        ))}
                      </ul>
                      <div className="mt-3 text-sm">
                        <strong>Retention:</strong> <span className="text-gray-600">{type.retention}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* How We Use Cookies */}
            <Card className="mb-12 shadow-lg border-0">
              <CardHeader>
                <CardTitle className="text-2xl">How We Use Cookies</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Website Functionality</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                      <li>Remember your login status</li>
                      <li>Store items in your shopping cart</li>
                      <li>Save your language preferences</li>
                      <li>Maintain security during your session</li>
                      <li>Enable chat functionality</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Analytics & Improvement</h4>
                    <ul className="list-disc list-inside space-y-1 text-gray-700 text-sm">
                      <li>Understand how visitors use our site</li>
                      <li>Identify popular content and features</li>
                      <li>Measure website performance</li>
                      <li>Test new features and designs</li>
                      <li>Fix bugs and improve user experience</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Managing Cookies */}
            <Card className="mb-12 shadow-lg border-0">
              <CardHeader>
                <CardTitle className="text-2xl">Managing Your Cookie Preferences</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold mb-2">Browser Settings</h4>
                    <p className="text-gray-700 mb-3">
                      You can control cookies through your browser settings. Here's how to manage cookies in popular browsers:
                    </p>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <ul className="space-y-2 text-sm">
                        <li><strong>Chrome:</strong> Settings → Privacy and Security → Cookies and other site data</li>
                        <li><strong>Firefox:</strong> Options → Privacy & Security → Cookies and Site Data</li>
                        <li><strong>Safari:</strong> Preferences → Privacy → Manage Website Data</li>
                        <li><strong>Edge:</strong> Settings → Site permissions → Cookies and site data</li>
                      </ul>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Cookie Banner</h4>
                    <p className="text-gray-700">
                      When you first visit our website, you'll see a cookie banner where you can accept or customize your cookie preferences. You can change these preferences at any time by clicking the cookie settings link in our footer.
                    </p>
                  </div>

                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-yellow-800 mb-2">Important Note</h4>
                    <p className="text-yellow-700 text-sm">
                      Disabling certain cookies may affect the functionality of our website. Essential cookies cannot be disabled as they are necessary for the website to function properly.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Third-Party Services */}
            <Card className="mb-12 shadow-lg border-0">
              <CardHeader>
                <CardTitle className="text-2xl">Third-Party Services</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-gray-700 mb-4">
                  We use several third-party services that may set cookies on your device:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">Analytics</h4>
                    <ul className="list-disc list-inside text-sm text-gray-700">
                      <li>Google Analytics - Website analytics</li>
                      <li>Hotjar - User behavior analysis</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Marketing</h4>
                    <ul className="list-disc list-inside text-sm text-gray-700">
                      <li>Facebook Pixel - Social media advertising</li>
                      <li>Google Ads - Search advertising</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Functionality</h4>
                    <ul className="list-disc list-inside text-sm text-gray-700">
                      <li>YouTube - Video embedding</li>
                      <li>Maps - Location services</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Communication</h4>
                    <ul className="list-disc list-inside text-sm text-gray-700">
                      <li>Intercom - Customer chat</li>
                      <li>Mailchimp - Email marketing</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Updates to Policy */}
            <Card className="mb-8 shadow-lg border-0">
              <CardHeader>
                <CardTitle className="text-2xl">Updates to This Policy</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-gray-700">
                  We may update this Cookie Policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. We will notify you of any material changes by updating the "Last Updated" date at the top of this policy.
                </p>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl text-orange-800">Questions About Cookies?</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-orange-700 mb-4">
                  If you have any questions about our use of cookies, please contact us:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-orange-600" />
                    <span className="text-orange-700"><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-orange-600" />
                    <span className="text-orange-700">+255 784 123 456</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Cookies;
