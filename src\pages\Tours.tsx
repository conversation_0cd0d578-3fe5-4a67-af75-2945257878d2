
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import TourCard from '@/components/tours/TourCard';
import TourFilters from '@/components/tours/TourFilters';
import { Button } from '@/components/ui/button';
import { Grid, List, Search, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useTours } from '@/hooks/useTours';
import { SearchFilters } from '@/types/firebase';
import { useToast } from '@/hooks/use-toast';

// Define the Filters interface for TourFilters component
interface Filters {
  category: string;
  accommodationLevel: string;
  priceRange: number[];
  duration: string;
  destinations: string[];
}

const Tours = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<Filters>({
    category: 'all',
    accommodationLevel: 'all',
    priceRange: [0, 10000],
    duration: 'all',
    destinations: []
  });

  const { tours, loading, error, searchTours } = useTours();

  // Initialize search from URL parameters
  useEffect(() => {
    const urlDestination = searchParams.get('destination');
    const urlSearch = searchParams.get('search');
    const urlDate = searchParams.get('date');
    const urlTravelers = searchParams.get('travelers');

    if (urlDestination || urlSearch) {
      const searchValue = urlDestination || urlSearch || '';
      setSearchTerm(searchValue);

      setFilters(prev => ({
        ...prev,
        destination: urlDestination || undefined
      }));

      // Show welcome message for search from hero
      if (urlDestination) {
        const dateText = urlDate ? ` on ${new Date(urlDate).toLocaleDateString()}` : '';
        const travelersText = urlTravelers ? ` for ${urlTravelers} travelers` : '';

        toast({
          title: "Search Applied",
          description: `Showing tours for ${urlDestination}${dateText}${travelersText}`,
        });

        // Automatically trigger search with the URL parameters
        setTimeout(() => {
          searchTours(searchValue, {
            ...filters,
            destination: urlDestination || undefined
          });
        }, 100);
      }
    }
  }, [searchParams, toast]);

  // Search tours when search term or filters change
  useEffect(() => {
    const hasActiveFilters = searchTerm ||
      (filters.category && filters.category !== 'all') ||
      (filters.accommodationLevel && filters.accommodationLevel !== 'all') ||
      (filters.duration && filters.duration !== 'all') ||
      filters.destinations.length > 0 ||
      (filters.priceRange[0] > 0 || filters.priceRange[1] < 10000);

    if (hasActiveFilters) {
      // Convert Filters to SearchFilters for the search function
      const searchFilters: SearchFilters = {
        category: filters.category !== 'all' ? filters.category : undefined,
        tourType: filters.accommodationLevel !== 'all' ? filters.accommodationLevel : undefined,
        priceRange: {
          min: filters.priceRange[0],
          max: filters.priceRange[1]
        },
        duration: filters.duration !== 'all' ? filters.duration : undefined,
        destination: filters.destinations.length > 0 ? filters.destinations[0] : undefined
      };
      searchTours(searchTerm, searchFilters);
    }
  }, [searchTerm, filters, searchTours]);

  const clearFilters = () => {
    setFilters({
      category: 'all',
      accommodationLevel: 'all',
      priceRange: [0, 10000],
      duration: 'all',
      destinations: []
    });
    setSearchTerm('');

    // Clear URL parameters
    setSearchParams({});

    toast({
      title: "Filters Cleared",
      description: "All search filters have been reset.",
    });
  };

  // Handle filter changes from TourFilters component
  const handleFiltersChange = (newFilters: Filters) => {
    setFilters(newFilters);
  };

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-16">
          <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-12 md:py-16">
            <div className="container mx-auto px-4">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bubblegum font-bold mb-3 md:mb-4">Safari Tours</h1>
              <p className="text-lg md:text-xl max-w-2xl">
                Discover our carefully curated collection of safari experiences in Tanzania
              </p>
            </div>
          </div>
          <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Loading tours...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="pt-16">
          <div className="bg-gradient-to-r from-orange-600 to-red-600 text-white py-12 md:py-16">
            <div className="container mx-auto px-4">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bubblegum font-bold mb-3 md:mb-4">Safari Tours</h1>
              <p className="text-lg md:text-xl max-w-2xl">
                Discover our carefully curated collection of safari experiences in Tanzania
              </p>
            </div>
          </div>
          <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main className="pt-16">
        {/* Page Header */}
        <div
          className="relative text-white py-12 md:py-16 overflow-hidden bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: 'url("https://qconvxhtavuzjdwuowpt.supabase.co/storage/v1/object/public/photoz/image%20(5).png")'
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-orange-600/80 to-red-600/80" />
          <div className="relative z-10 container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bubblegum font-bold mb-3 md:mb-4">Safari Tours</h1>
            <p className="text-lg md:text-xl max-w-2xl">
              Discover our carefully curated collection of safari experiences in Tanzania
            </p>
          </div>
        </div>

        <div className="container mx-auto px-4 py-6 md:py-8">
          <div className="flex flex-col lg:flex-row gap-6 md:gap-8">
            {/* Filters Sidebar */}
            <div className="lg:w-1/4">
              <div className="lg:sticky lg:top-24">
                <TourFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                />
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:w-3/4">
              {/* Search and View Controls */}
              <div className="flex flex-col sm:flex-row gap-3 md:gap-4 mb-4 md:mb-6">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search tours..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 text-sm md:text-base"
                  />
                </div>
                <div className="flex gap-2 self-start sm:self-auto">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="icon"
                    onClick={() => setViewMode('grid')}
                    className="h-9 w-9 md:h-10 md:w-10"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="icon"
                    onClick={() => setViewMode('list')}
                    className="h-9 w-9 md:h-10 md:w-10"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Results Summary */}
              <div className="mb-4 md:mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <p className="text-gray-600 text-sm md:text-base">
                    Showing {tours.length} tours
                    {searchTerm && ` for "${searchTerm}"`}
                    {filters.destinations.length > 0 && ` in ${filters.destinations.join(', ')}`}
                  </p>
                  {(searchTerm || filters.destinations.length > 0 || filters.category !== 'all' || filters.accommodationLevel !== 'all' || filters.duration !== 'all' || searchParams.toString()) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearFilters}
                      className="text-xs md:text-sm"
                    >
                      Clear All Filters
                    </Button>
                  )}
                </div>
              </div>

              {/* Tours Grid/List */}
              <div className={viewMode === 'grid' 
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6' 
                : 'space-y-4 md:space-y-6'}>
                {tours.map((tour) => (
                  <TourCard 
                    key={tour.id} 
                    tour={{
                      id: tour.id,
                      title: tour.title,
                      description: tour.description,
                      price: tour.price,
                      duration: tour.duration,
                      image: (tour.images && tour.images[0]) || 'photo-1472396961693-142e6e269027',
                      category: tour.category,
                      accommodationLevel: tour.accommodationLevel,
                      destinations: tour.destinations,
                      rating: tour.rating,
                      reviews: tour.reviewCount
                    }}
                    viewMode={viewMode}
                  />
                ))}
              </div>

              {tours.length === 0 && (
                <div className="text-center py-8 md:py-12">
                  <p className="text-gray-500 text-base md:text-lg mb-4">No tours found matching your criteria.</p>
                  <Button 
                    onClick={clearFilters}
                    className="text-sm md:text-base px-4 md:px-6"
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Tours;
