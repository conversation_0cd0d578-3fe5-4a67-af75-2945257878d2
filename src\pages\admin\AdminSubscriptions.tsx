import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { FirebaseService } from '@/services/firebase';
import { Mail, Download, Search, Calendar, Users, Trash2 } from 'lucide-react';
import { format } from 'date-fns';

interface NewsletterSubscription {
  id: string;
  email: string;
  subscribedAt: Date;
  status: 'active' | 'unsubscribed';
  source?: string;
}

const AdminSubscriptions = () => {
  const [subscriptions, setSubscriptions] = useState<NewsletterSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<NewsletterSubscription[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    loadSubscriptions();
  }, []);

  useEffect(() => {
    filterSubscriptions();
  }, [subscriptions, searchTerm]);

  const loadSubscriptions = async () => {
    try {
      setLoading(true);
      const data = await FirebaseService.getNewsletterSubscriptions();
      const formattedData = data.map((sub: any) => ({
        id: sub.id,
        email: sub.email,
        subscribedAt: sub.subscribedAt?.toDate() || new Date(),
        status: sub.status || 'active',
        source: sub.source || 'website'
      }));
      setSubscriptions(formattedData);
    } catch (error) {
      console.error('Error loading subscriptions:', error);
      toast({
        title: "Error",
        description: "Failed to load newsletter subscriptions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filterSubscriptions = () => {
    if (!searchTerm) {
      setFilteredSubscriptions(subscriptions);
      return;
    }

    const filtered = subscriptions.filter(sub =>
      sub.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredSubscriptions(filtered);
  };

  const handleUnsubscribe = async (id: string, email: string) => {
    try {
      await FirebaseService.updateNewsletterSubscription(id, { status: 'unsubscribed' });
      setSubscriptions(prev => 
        prev.map(sub => 
          sub.id === id ? { ...sub, status: 'unsubscribed' as const } : sub
        )
      );
      toast({
        title: "Success",
        description: `${email} has been unsubscribed`,
      });
    } catch (error) {
      console.error('Error unsubscribing:', error);
      toast({
        title: "Error",
        description: "Failed to unsubscribe user",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: string, email: string) => {
    if (!confirm(`Are you sure you want to delete ${email} from the newsletter list?`)) {
      return;
    }

    try {
      await FirebaseService.deleteNewsletterSubscription(id);
      setSubscriptions(prev => prev.filter(sub => sub.id !== id));
      toast({
        title: "Success",
        description: `${email} has been deleted from the newsletter list`,
      });
    } catch (error) {
      console.error('Error deleting subscription:', error);
      toast({
        title: "Error",
        description: "Failed to delete subscription",
        variant: "destructive",
      });
    }
  };

  const exportSubscriptions = () => {
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    const csvContent = [
      ['Email', 'Subscribed Date', 'Source'],
      ...activeSubscriptions.map(sub => [
        sub.email,
        format(sub.subscribedAt, 'yyyy-MM-dd HH:mm:ss'),
        sub.source || 'website'
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `newsletter-subscriptions-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast({
      title: "Success",
      description: "Newsletter subscriptions exported successfully",
    });
  };

  const activeCount = subscriptions.filter(sub => sub.status === 'active').length;
  const unsubscribedCount = subscriptions.filter(sub => sub.status === 'unsubscribed').length;

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Newsletter Subscriptions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Active Subscribers</p>
                <p className="text-2xl font-bold text-green-600">{activeCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Mail className="h-5 w-5 text-gray-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Subscriptions</p>
                <p className="text-2xl font-bold">{subscriptions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Unsubscribed</p>
                <p className="text-2xl font-bold text-red-600">{unsubscribedCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-xl">Newsletter Subscriptions</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Manage newsletter subscribers and export email lists
              </p>
            </div>
            <Button onClick={exportSubscriptions} className="w-full sm:w-auto">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by email address..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Subscriptions Table */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Email</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Subscribed Date</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSubscriptions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                      {searchTerm ? 'No subscriptions found matching your search.' : 'No newsletter subscriptions found.'}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredSubscriptions.map((subscription) => (
                    <TableRow key={subscription.id}>
                      <TableCell className="font-medium">{subscription.email}</TableCell>
                      <TableCell>
                        <Badge 
                          variant={subscription.status === 'active' ? 'default' : 'secondary'}
                          className={subscription.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                        >
                          {subscription.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {format(subscription.subscribedAt, 'MMM dd, yyyy HH:mm')}
                      </TableCell>
                      <TableCell className="capitalize">{subscription.source}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          {subscription.status === 'active' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleUnsubscribe(subscription.id, subscription.email)}
                            >
                              Unsubscribe
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(subscription.id, subscription.email)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminSubscriptions;
